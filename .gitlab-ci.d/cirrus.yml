# Jobs that we delegate to Cirrus CI because they require an operating
# system other than Linux. These jobs will only run if the required
# setup has been performed on the GitLab account.
#
# The Cirrus CI configuration is generated by replacing target-specific
# variables in a generic template: some of these variables are provided
# when the GitLab CI job is defined, others are taken from a shell
# snippet generated using lcitool.
#
# Note that the $PATH environment variable has to be treated with
# special care, because we can't just override it at the GitLab CI job
# definition level or we risk breaking it completely.
.cirrus_build_job:
  extends: .base_job_template
  stage: build
  image: registry.gitlab.com/libvirt/libvirt-ci/cirrus-run:latest
  needs: []
  allow_failure:
    exit_codes: 3
  # 20 mins larger than "timeout_in" in cirrus/build.yml
  # as there's often a 5-10 minute delay before Cirrus CI
  # actually starts the task
  timeout: 80m
  script:
    - set -o allexport
    - source .gitlab-ci.d/cirrus/$NAME.vars
    - set +o allexport
    - cirrus-vars <.gitlab-ci.d/cirrus/build.yml >.gitlab-ci.d/cirrus/$NAME.yml
    - cat .gitlab-ci.d/cirrus/$NAME.yml
    - cirrus-run -v --show-build-log always .gitlab-ci.d/cirrus/$NAME.yml
  variables:
    QEMU_JOB_CIRRUS: 1

x64-freebsd-14-build:
  extends: .cirrus_build_job
  variables:
    NAME: freebsd-14
    CIRRUS_VM_INSTANCE_TYPE: freebsd_instance
    CIRRUS_VM_IMAGE_SELECTOR: image_family
    CIRRUS_VM_IMAGE_NAME: freebsd-14-2
    CIRRUS_VM_CPUS: 8
    CIRRUS_VM_RAM: 8G
    UPDATE_COMMAND: pkg update; pkg upgrade -y
    INSTALL_COMMAND: pkg install -y
    CONFIGURE_ARGS: --target-list-exclude=arm-softmmu,i386-softmmu,microblaze-softmmu,mips64el-softmmu,mipsel-softmmu,mips-softmmu,ppc-softmmu,sh4eb-softmmu,xtensa-softmmu
    TEST_TARGETS: check

aarch64-macos-build:
  extends: .cirrus_build_job
  variables:
    NAME: macos-14
    CIRRUS_VM_INSTANCE_TYPE: macos_instance
    CIRRUS_VM_IMAGE_SELECTOR: image
    CIRRUS_VM_IMAGE_NAME: ghcr.io/cirruslabs/macos-runner:sonoma
    UPDATE_COMMAND: brew update
    INSTALL_COMMAND: brew install
    PATH_EXTRA: /opt/homebrew/ccache/libexec:/opt/homebrew/gettext/bin
    PKG_CONFIG_PATH: /opt/homebrew/curl/lib/pkgconfig:/opt/homebrew/ncurses/lib/pkgconfig:/opt/homebrew/readline/lib/pkgconfig
    CONFIGURE_ARGS: --target-list-exclude=arm-softmmu,i386-softmmu,microblazeel-softmmu,mips64-softmmu,mipsel-softmmu,mips-softmmu,ppc-softmmu,sh4-softmmu,xtensaeb-softmmu
    TEST_TARGETS: check-unit check-block check-qapi-schema check-softfloat check-qtest-x86_64
