@CIRRUS_VM_INSTANCE_TYPE@:
  @CIRRUS_VM_IMAGE_SELECTOR@: @CIRRUS_VM_IMAGE_NAME@
  cpu: @CIRRUS_VM_CPUS@
  memory: @CIRRUS_VM_RAM@

env:
  CIRRUS_CLONE_DEPTH: 1
  CI_REPOSITORY_URL: "@CI_REPOSITORY_URL@"
  CI_COMMIT_REF_NAME: "@CI_COMMIT_REF_NAME@"
  CI_COMMIT_SHA: "@CI_COMMIT_SHA@"
  PATH: "@PATH_EXTRA@:$PATH"
  PKG_CONFIG_PATH: "@PKG_CONFIG_PATH@"
  PYTHON: "@PYTHON@"
  MAKE: "@MAKE@"
  CONFIGURE_ARGS: "@CONFIGURE_ARGS@"
  TEST_TARGETS: "@TEST_TARGETS@"

build_task:
  # A little shorter than GitLab timeout in ../cirrus.yml
  timeout_in: 60m
  install_script:
    - @UPDATE_COMMAND@
    - @INSTALL_COMMAND@ @PKGS@
    - if test -n "@PYPI_PKGS@" ; then PYLIB=$(@PYTHON@ -c 'import sysconfig; print(sysconfig.get_path("stdlib"))'); rm -f $PYLIB/EXTERNALLY-MANAGED; @PIP3@ install @PYPI_PKGS@ ; fi
  clone_script:
    - git clone --depth 100 "$CI_REPOSITORY_URL" .
    - git fetch origin "$CI_COMMIT_REF_NAME"
    - git reset --hard "$CI_COMMIT_SHA"
  step_script:
    - mkdir build
    - cd build
    - ../configure --enable-werror $CONFIGURE_ARGS
      || { cat config.log meson-logs/meson-log.txt; exit 1; }
    - $MAKE -j$(sysctl -n hw.ncpu)
    - for TARGET in $TEST_TARGETS ;
      do
        $MAKE -j$(sysctl -n hw.ncpu) $TARGET V=1 ;
      done
  always:
    build_result_artifacts:
      path: build/meson-logs/*log.txt
      type: text/plain
