.cross_system_build_job:
  extends: .base_job_template
  stage: build
  image: $CI_REGISTRY_IMAGE/qemu/$IMAGE:$QEMU_CI_CONTAINER_TAG
  cache:
    paths:
      - ccache
    key: "$CI_JOB_NAME"
    when: always
  timeout: 80m
  before_script:
    - source scripts/ci/gitlab-ci-section
    - section_start setup "Pre-script setup"
    - JOBS=$(expr $(nproc) + 1)
    - cat /packages.txt
    - section_end setup
  script:
    - export CCACHE_BASEDIR="$(pwd)"
    - export CCACHE_DIR="$CCACHE_BASEDIR/ccache"
    - export CCACHE_MAXSIZE="500M"
    - export PATH="$CCACHE_WRAPPERSDIR:$PATH"
    - mkdir build
    - cd build
    - ccache --zero-stats
    - section_start configure "Running configure"
    - ../configure --enable-werror --disable-docs --enable-fdt=system
        --disable-user $QEMU_CONFIGURE_OPTS $EXTRA_CONFIGURE_OPTS
        --target-list-exclude="arm-softmmu
          i386-softmmu microblaze-softmmu mips-softmmu mipsel-softmmu
          mips64-softmmu ppc-softmmu riscv32-softmmu sh4-softmmu
          sparc-softmmu xtensa-softmmu $CROSS_SKIP_TARGETS"
    - section_end configure
    - section_start build "Building QEMU"
    - make -j"$JOBS" all check-build
    - section_end build
    - section_start test "Running tests"
    - if test -n "$MAKE_CHECK_ARGS";
      then
        $MAKE -j"$JOBS" $MAKE_CHECK_ARGS ;
      fi
    - section_end test
    - section_start installer "Building the installer"
    - if grep -q "EXESUF=.exe" config-host.mak;
      then make installer;
      version="$(git describe --match v[0-9]* 2>/dev/null || git rev-parse --short HEAD)";
      mv -v qemu-setup*.exe qemu-setup-${version}.exe;
      fi
    - section_end installer
    - ccache --show-stats

# Job to cross-build specific accelerators.
#
# Set the $ACCEL variable to select the specific accelerator (default to
# KVM), and set extra options (such disabling other accelerators) via the
# $EXTRA_CONFIGURE_OPTS variable.
.cross_accel_build_job:
  extends: .base_job_template
  stage: build
  image: $CI_REGISTRY_IMAGE/qemu/$IMAGE:$QEMU_CI_CONTAINER_TAG
  timeout: 60m
  cache:
    paths:
      - ccache/
    key: "$CI_JOB_NAME"
  before_script:
    - source scripts/ci/gitlab-ci-section
    - JOBS=$(expr $(nproc) + 1)
  script:
    - export CCACHE_BASEDIR="$(pwd)"
    - export CCACHE_DIR="$CCACHE_BASEDIR/ccache"
    - export CCACHE_MAXSIZE="500M"
    - export PATH="$CCACHE_WRAPPERSDIR:$PATH"
    - mkdir build
    - cd build
    - section_start configure "Running configure"
    - ../configure --enable-werror --disable-docs $QEMU_CONFIGURE_OPTS
        --disable-tools --enable-${ACCEL:-kvm} $EXTRA_CONFIGURE_OPTS
    - section_end configure
    - section_start build "Building QEMU"
    - make -j"$JOBS" all check-build
    - section_end build
    - section_start test "Running tests"
    - if test -n "$MAKE_CHECK_ARGS";
      then
        $MAKE -j"$JOBS" $MAKE_CHECK_ARGS ;
      fi
    - section_end test

.cross_user_build_job:
  extends: .base_job_template
  stage: build
  image: $CI_REGISTRY_IMAGE/qemu/$IMAGE:$QEMU_CI_CONTAINER_TAG
  cache:
    paths:
      - ccache/
    key: "$CI_JOB_NAME"
  before_script:
    - source scripts/ci/gitlab-ci-section
    - JOBS=$(expr $(nproc) + 1)
  script:
    - export CCACHE_BASEDIR="$(pwd)"
    - export CCACHE_DIR="$CCACHE_BASEDIR/ccache"
    - export CCACHE_MAXSIZE="500M"
    - mkdir build
    - cd build
    - section_start configure "Running configure"
    - ../configure --enable-werror --disable-docs $QEMU_CONFIGURE_OPTS
        --disable-system --target-list-exclude="aarch64_be-linux-user
          alpha-linux-user m68k-linux-user microblazeel-linux-user
          or1k-linux-user ppc-linux-user sparc-linux-user
          xtensa-linux-user $CROSS_SKIP_TARGETS"
    - section_end configure
    - section_start build "Building QEMU"
    - make -j"$JOBS" all check-build
    - section_end build
    - section_start test "Running tests"
    - if test -n "$MAKE_CHECK_ARGS";
      then
        $MAKE -j"$JOBS" $MAKE_CHECK_ARGS ;
      fi
    - section_end test

# We can still run some tests on some of our cross build jobs. They can add this
# template to their extends to save the build logs and test results
.cross_test_artifacts:
  artifacts:
    name: "$CI_JOB_NAME-$CI_COMMIT_REF_SLUG"
    when: always
    expire_in: 7 days
    paths:
      - build/meson-logs/testlog.txt
    reports:
      junit: build/meson-logs/testlog.junit.xml
