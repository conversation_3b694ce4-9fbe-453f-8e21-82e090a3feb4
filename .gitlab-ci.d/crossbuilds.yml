include:
  - local: '/.gitlab-ci.d/crossbuild-template.yml'

cross-armhf-user:
  extends: .cross_user_build_job
  needs:
    job: armhf-debian-cross-container
  variables:
    IMAGE: debian-armhf-cross

cross-arm64-system:
  extends: .cross_system_build_job
  needs:
    job: arm64-debian-cross-container
  variables:
    IMAGE: debian-arm64-cross

cross-arm64-user:
  extends: .cross_user_build_job
  needs:
    job: arm64-debian-cross-container
  variables:
    IMAGE: debian-arm64-cross

cross-arm64-kvm-only:
  extends: .cross_accel_build_job
  needs:
    job: arm64-debian-cross-container
  variables:
    IMAGE: debian-arm64-cross
    EXTRA_CONFIGURE_OPTS: --disable-tcg --without-default-features

cross-i686-system:
  extends:
    - .cross_system_build_job
    - .cross_test_artifacts
  needs:
    job: i686-debian-cross-container
  variables:
    IMAGE: debian-i686-cross
    EXTRA_CONFIGURE_OPTS: --disable-kvm
    MAKE_CHECK_ARGS: check-qtest

cross-i686-user:
  extends:
    - .cross_user_build_job
    - .cross_test_artifacts
  needs:
    job: i686-debian-cross-container
  variables:
    IMAGE: debian-i686-cross
    MAKE_CHECK_ARGS: check

cross-i686-tci:
  extends:
    - .cross_accel_build_job
    - .cross_test_artifacts
  timeout: 60m
  needs:
    job: i686-debian-cross-container
  variables:
    IMAGE: debian-i686-cross
    ACCEL: tcg-interpreter
    EXTRA_CONFIGURE_OPTS: --target-list=i386-softmmu,i386-linux-user,arm-softmmu,arm-linux-user,ppc-softmmu,ppc-linux-user --disable-plugins --disable-kvm
    # Force tests to run with reduced parallelism, to see whether this
    # reduces the flakiness of this CI job. The CI
    # environment by default shows us 8 CPUs and so we
    # would otherwise be using a parallelism of 9.
    MAKE_CHECK_ARGS: check check-tcg -j2

cross-mipsel-system:
  extends: .cross_system_build_job
  needs:
    job: mipsel-debian-cross-container
  variables:
    IMAGE: debian-mipsel-cross

cross-mipsel-user:
  extends: .cross_user_build_job
  needs:
    job: mipsel-debian-cross-container
  variables:
    IMAGE: debian-mipsel-cross

cross-mips64el-system:
  extends: .cross_system_build_job
  needs:
    job: mips64el-debian-cross-container
  variables:
    IMAGE: debian-mips64el-cross

cross-mips64el-user:
  extends: .cross_user_build_job
  needs:
    job: mips64el-debian-cross-container
  variables:
    IMAGE: debian-mips64el-cross

cross-ppc64el-system:
  extends: .cross_system_build_job
  needs:
    job: ppc64el-debian-cross-container
  variables:
    IMAGE: debian-ppc64el-cross

cross-ppc64el-user:
  extends: .cross_user_build_job
  needs:
    job: ppc64el-debian-cross-container
  variables:
    IMAGE: debian-ppc64el-cross

cross-ppc64el-kvm-only:
  extends: .cross_accel_build_job
  needs:
    job: ppc64el-debian-cross-container
  variables:
    IMAGE: debian-ppc64el-cross
    EXTRA_CONFIGURE_OPTS: --disable-tcg --without-default-devices

cross-riscv64-system:
  extends: .cross_system_build_job
  needs:
    job: riscv64-debian-cross-container
  variables:
    IMAGE: debian-riscv64-cross

cross-riscv64-user:
  extends: .cross_user_build_job
  needs:
    job: riscv64-debian-cross-container
  variables:
    IMAGE: debian-riscv64-cross

cross-s390x-system:
  extends: .cross_system_build_job
  needs:
    job: s390x-debian-cross-container
  variables:
    IMAGE: debian-s390x-cross

cross-s390x-user:
  extends: .cross_user_build_job
  needs:
    job: s390x-debian-cross-container
  variables:
    IMAGE: debian-s390x-cross

cross-s390x-kvm-only:
  extends: .cross_accel_build_job
  needs:
    job: s390x-debian-cross-container
  variables:
    IMAGE: debian-s390x-cross
    EXTRA_CONFIGURE_OPTS: --disable-tcg --enable-trace-backends=ftrace

cross-mips64el-kvm-only:
  extends: .cross_accel_build_job
  needs:
    job: mips64el-debian-cross-container
  variables:
    IMAGE: debian-mips64el-cross
    EXTRA_CONFIGURE_OPTS: --disable-tcg --target-list=mips64el-softmmu

cross-win64-system:
  extends: .cross_system_build_job
  needs:
    job: win64-fedora-cross-container
  variables:
    IMAGE: fedora-win64-cross
    EXTRA_CONFIGURE_OPTS: --enable-fdt=internal --disable-plugins
    CROSS_SKIP_TARGETS: alpha-softmmu avr-softmmu hppa-softmmu
                        m68k-softmmu microblazeel-softmmu
                        or1k-softmmu rx-softmmu sh4eb-softmmu sparc64-softmmu
                        tricore-softmmu xtensaeb-softmmu
  artifacts:
    when: on_success
    paths:
      - build/qemu-setup*.exe

cross-amd64-xen-only:
  extends: .cross_accel_build_job
  needs:
    job: amd64-debian-cross-container
  variables:
    IMAGE: debian-amd64-cross
    ACCEL: xen
    EXTRA_CONFIGURE_OPTS: --disable-tcg --disable-kvm

cross-arm64-xen-only:
  extends: .cross_accel_build_job
  needs:
    job: arm64-debian-cross-container
  variables:
    IMAGE: debian-arm64-cross
    ACCEL: xen
    EXTRA_CONFIGURE_OPTS: --disable-tcg --disable-kvm
