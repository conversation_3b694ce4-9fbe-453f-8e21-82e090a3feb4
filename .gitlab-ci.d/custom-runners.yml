# The CI jobs defined here require GitLab runners installed and
# registered on machines that match their operating system names,
# versions and architectures.  This is in contrast to the other CI
# jobs that are intended to run on GitLab's "shared" runners.

# Different than the default approach on "shared" runners, based on
# containers, the custom runners have no such *requirement*, as those
# jobs should be capable of running on operating systems with no
# compatible container implementation, or no support from
# gitlab-runner.  To avoid problems that gitlab-runner can cause while
# reusing the GIT repository, let's enable the clone strategy, which
# guarantees a fresh repository on each job run.

# All custom runners can extend this template to upload the testlog
# data as an artifact and also feed the junit report
.custom_runner_template:
  extends: .base_job_template
  variables:
    GIT_STRATEGY: clone
    GIT_FETCH_EXTRA_FLAGS: --no-tags --prune --quiet
  artifacts:
    name: "$CI_JOB_NAME-$CI_COMMIT_REF_SLUG"
    expire_in: 7 days
    when: always
    paths:
      - build/build.ninja
      - build/meson-logs
    reports:
      junit: build/meson-logs/testlog.junit.xml

include:
  - local: '/.gitlab-ci.d/custom-runners/ubuntu-22.04-s390x.yml'
  - local: '/.gitlab-ci.d/custom-runners/ubuntu-22.04-aarch64.yml'
  - local: '/.gitlab-ci.d/custom-runners/ubuntu-22.04-aarch32.yml'
