# All ubuntu-22.04 jobs should run successfully in an environment
# setup by the scripts/ci/setup/ubuntu/build-environment.yml task
# "Install basic packages to build QEMU on Ubuntu 22.04"

ubuntu-22.04-s390x-all-linux:
 extends: .custom_runner_template
 needs: []
 stage: build
 tags:
 - ubuntu_22.04
 - s390x
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
 - if: "$S390X_RUNNER_AVAILABLE"
 script:
 - mkdir build
 - cd build
 - ../configure --enable-debug --disable-system --disable-tools --disable-docs
   || { cat config.log meson-logs/meson-log.txt; exit 1; }
 - make --output-sync -j`nproc`
 - make --output-sync check-tcg
 - make --output-sync -j`nproc` check

ubuntu-22.04-s390x-all-system:
 extends: .custom_runner_template
 needs: []
 stage: build
 tags:
 - ubuntu_22.04
 - s390x
 timeout: 75m
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
   allow_failure: true
 - if: "$S390X_RUNNER_AVAILABLE"
   allow_failure: true
 script:
 - mkdir build
 - cd build
 - ../configure --disable-user
   || { cat config.log meson-logs/meson-log.txt; exit 1; }
 - make --output-sync -j`nproc`
 - make --output-sync -j`nproc` check

ubuntu-22.04-s390x-alldbg:
 extends: .custom_runner_template
 needs: []
 stage: build
 tags:
 - ubuntu_22.04
 - s390x
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
   when: manual
   allow_failure: true
 - if: "$S390X_RUNNER_AVAILABLE"
   when: manual
   allow_failure: true
 script:
 - mkdir build
 - cd build
 - ../configure --enable-debug
   || { cat config.log meson-logs/meson-log.txt; exit 1; }
 - make clean
 - make --output-sync -j`nproc`
 - make --output-sync -j`nproc` check

ubuntu-22.04-s390x-clang:
 extends: .custom_runner_template
 needs: []
 stage: build
 tags:
 - ubuntu_22.04
 - s390x
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
   when: manual
   allow_failure: true
 - if: "$S390X_RUNNER_AVAILABLE"
   when: manual
   allow_failure: true
 script:
 - mkdir build
 - cd build
 - ../configure --cc=clang --cxx=clang++ --enable-ubsan
   || { cat config.log meson-logs/meson-log.txt; exit 1; }
 - make --output-sync -j`nproc`
 - make --output-sync -j`nproc` check

ubuntu-22.04-s390x-tci:
 needs: []
 stage: build
 tags:
 - ubuntu_22.04
 - s390x
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
   when: manual
   allow_failure: true
 - if: "$S390X_RUNNER_AVAILABLE"
   when: manual
   allow_failure: true
 script:
 - mkdir build
 - cd build
 - ../configure --enable-tcg-interpreter
   || { cat config.log meson-logs/meson-log.txt; exit 1; }
 - make --output-sync -j`nproc`

ubuntu-22.04-s390x-notcg:
 extends: .custom_runner_template
 needs: []
 stage: build
 tags:
 - ubuntu_22.04
 - s390x
 rules:
 - if: '$CI_PROJECT_NAMESPACE == "qemu-project" && $CI_COMMIT_BRANCH =~ /^staging/'
   when: manual
   allow_failure: true
 - if: "$S390X_RUNNER_AVAILABLE"
   when: manual
   allow_failure: true
 script:
 - mkdir build
 - cd build
 - ../configure --disable-tcg
   || { cat config.log meson-logs/meson-log.txt; exit 1; }
 - make --output-sync -j`nproc`
 - make --output-sync -j`nproc` check
